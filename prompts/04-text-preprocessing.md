# Step 4: Interview Text Preprocessing

## Step Overview
Interview text preprocessing is the foundational step that cleans, standardizes, and prepares raw interview transcripts for systematic analysis. This step transforms unstructured interview recordings and transcripts into consistent, analyzable formats while preserving participant voice, conversational context, and the integrity of question-answer exchanges.

## AI Agent Role
The AI agent acts as an **Interview Data Specialist** responsible for:
- Standardizing interview transcript formats and encoding
- Preserving speaker identification and conversational flow
- Correcting transcription errors while maintaining participant voice
- Structuring interview content into analyzable question-response segments
- Maintaining interview session integrity and participant traceability
- Identifying and preserving contextual cues and emotional indicators

## Input Requirements
- **Raw interview transcripts** including:
  - Audio-to-text transcriptions from interview sessions
  - Manual interview notes and observations
  - Video transcript outputs with speaker identification
- **Interview metadata** including:
  - Participant identifier and demographic information
  - Interview session details (date, duration, interviewer)
  - Interview guide/question framework used
  - Recording quality and transcription confidence scores
- **Interview-specific parameters** such as:
  - Speaker identification accuracy requirements
  - Question-answer pair preservation rules
  - Contextual marker preservation (pauses, emotions, interruptions)

## Processing Guidelines

### 1. Interview Transcript Standardization
- Convert all interview text to UTF-8 encoding with consistent formatting
- Standardize speaker identification markers (Interviewer:, Participant:, P1:, etc.)
- Normalize whitespace while preserving meaningful pauses and breaks
- Standardize quotation marks and preserve participant's authentic voice

### 2. Interview-Specific Content Cleaning
- Correct obvious transcription errors while preserving participant language patterns
- Remove filler words (um, uh, like) only when they don't convey meaning
- Clean up false starts and incomplete sentences with clear notation
- Preserve interviewer questions and participant responses as distinct units
- Maintain timestamps for significant moments or topic transitions

### 3. Conversational Structure Preservation
- Maintain clear speaker identification throughout the transcript
- Preserve question-answer pairs and conversational flow
- Keep interviewer prompts and follow-up questions intact
- Maintain participant's natural speech patterns and colloquialisms
- Preserve emotional indicators and contextual cues (laughter, pauses, emphasis)

### 4. Interview Quality Validation
- Flag incomplete responses or unclear participant statements
- Identify potential interviewer bias or leading questions
- Assess participant engagement levels and response quality
- Document transcription confidence levels and unclear segments
- Maintain traceability to original audio/video sources

## Output Format
```json
{
  "processed_interviews": [
    {
      "interview_id": "INT_001",
      "participant_id": "P001",
      "interview_metadata": {
        "date": "2024-01-15",
        "duration_minutes": 45,
        "interviewer": "researcher_001",
        "interview_guide_version": "v2.1",
        "recording_quality": "high"
      },
      "original_transcript_length": 8500,
      "processed_transcript_length": 8200,
      "preprocessing_notes": "Corrected 5 transcription errors, standardized speaker labels, preserved 3 emotional indicators",
      "processed_transcript": "Interviewer: Can you tell me about your first experience with the platform?\n\nParticipant: Well, honestly, it was pretty confusing at first [laughs]. I couldn't figure out where anything was...",
      "question_answer_pairs": [
        {
          "question_id": "Q1",
          "interviewer_question": "Can you tell me about your first experience with the platform?",
          "participant_response": "Well, honestly, it was pretty confusing at first [laughs]. I couldn't figure out where anything was...",
          "response_completeness": "complete",
          "emotional_indicators": ["laughter", "hesitation"]
        }
      ],
      "quality_assessment": {
        "transcription_confidence": 0.94,
        "participant_engagement": "high",
        "response_completeness": 0.89,
        "interviewer_bias_flags": 0,
        "unclear_segments": 2
      },
      "processing_flags": ["minor_transcription_corrections", "emotional_markers_preserved"]
    }
  ],
  "processing_summary": {
    "total_interviews": 12,
    "total_participants": 12,
    "total_original_words": 95000,
    "total_processed_words": 92500,
    "average_interview_duration": 42,
    "common_issues_found": ["transcription_errors", "speaker_identification", "incomplete_responses"],
    "quality_metrics": {
      "average_transcription_confidence": 0.91,
      "average_participant_engagement": 0.87,
      "interviews_requiring_review": 1
    }
  }
}
```

## Quality Criteria
- **Interview Integrity**: 95%+ of participant voice and meaning preserved
- **Speaker Consistency**: Clear, consistent speaker identification throughout
- **Conversational Flow**: Natural question-answer progression maintained
- **Context Preservation**: Emotional cues and contextual markers retained
- **Traceability**: All changes documented with reference to original audio/video

## Error Handling
- **Poor transcription quality**: Flag for manual review, note confidence levels
- **Unclear speaker identification**: Attempt resolution, flag ambiguous sections
- **Incomplete responses**: Mark partial responses, note interruptions or technical issues
- **Interviewer bias detection**: Flag leading questions or inappropriate prompts
- **Processing failures**: Preserve original transcript, log issues, continue with batch

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.1 for consistent, conservative processing
- Configure max tokens based on document size (typically 4000-8000)
- Enable streaming for large documents

### Input Configuration
- Accept JSON array of documents from previous step
- Configure file upload handling for direct document input
- Set up batch processing for multiple documents
- Include metadata preservation settings

### Output Configuration
- Structure output as JSON for next step consumption
- Include quality metrics for monitoring
- Set up error logging and flagging system
- Configure review queue for problematic documents

## Example Prompts

### Primary Interview Processing Prompt
```
You are an interview transcript preprocessing specialist. Clean and standardize the following interview transcript while preserving participant voice, conversational flow, and contextual meaning.

INTERVIEW PREPROCESSING INSTRUCTIONS:
1. Standardize speaker identification (Interviewer:, Participant:)
2. Correct obvious transcription errors while preserving participant's natural speech
3. Maintain question-answer pairs and conversational structure
4. Preserve emotional indicators and contextual cues [laughter], [pause], etc.
5. Remove excessive filler words only when they don't convey meaning
6. Document any significant changes or unclear segments

INTERVIEW TRANSCRIPT:
{raw_interview_transcript}

INTERVIEW METADATA:
- Participant ID: {participant_id}
- Interview Date: {interview_date}
- Interviewer: {interviewer_name}
- Recording Quality: {recording_quality}

Provide the cleaned transcript with preserved question-answer structure and a summary of preprocessing changes made in the format requested.
```

### Interview Quality Assessment Prompt
```
Evaluate the quality of this interview transcript preprocessing:

ORIGINAL TRANSCRIPT: {original_interview_transcript}
PROCESSED TRANSCRIPT: {processed_interview_transcript}
PREPROCESSING CHANGES: {changes_made}

Rate the preprocessing quality (0-1) based on:
- Participant voice preservation
- Conversational flow maintenance
- Speaker identification accuracy
- Context and emotional cue preservation
- Question-answer pair integrity

Provide score and brief justification focusing on interview-specific quality factors.
```

### Interview Recovery Prompt
```
This interview transcript has significant quality issues. Attempt recovery:

PROBLEMATIC TRANSCRIPT: {corrupted_interview_text}
ISSUE TYPE: {transcription_error_type}
PARTICIPANT ID: {participant_id}

Recovery approach:
1. Identify speaker boundaries and question-answer pairs
2. Flag unclear or inaudible segments with timestamps
3. Preserve participant's authentic voice where possible
4. Note confidence levels for recovered content
5. Recommend manual review for critical unclear segments
```
