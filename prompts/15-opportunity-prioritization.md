# Step 15: Interview-Based Opportunity Prioritization

## Step Overview
Interview-based opportunity prioritization evaluates and ranks HMW questions and innovation opportunities derived from participant insights based on strategic value, participant impact, and implementation feasibility. This step creates a structured decision-making framework grounded in interview findings that guides resource allocation and strategic planning.

## AI Agent Role
The AI agent acts as an **Interview-Informed Strategic Portfolio Manager** responsible for:
- Evaluating opportunities against multiple criteria including participant impact and needs
- Creating prioritization frameworks that weight participant insights and business value
- Balancing competing priorities while maintaining focus on participant-identified needs
- Developing implementation roadmaps based on participant feedback and business constraints
- Providing strategic recommendations for resource allocation grounded in interview evidence

## Input Requirements
- **HMW questions** from Step 14 with:
  - Categorized design challenges and opportunities
  - Impact assessments and success criteria
  - Implementation constraints and requirements
  - Innovation potential and strategic alignment
- **Business context** including:
  - Strategic objectives and priorities
  - Resource availability and constraints
  - Timeline requirements and deadlines
  - Risk tolerance and success metrics
- **Prioritization parameters** such as:
  - Evaluation criteria and weights
  - Scoring methodologies
  - Portfolio balance requirements
  - Stakeholder input and preferences

## Processing Guidelines

### 1. Multi-Criteria Evaluation
- Assess opportunities against strategic, user, and business criteria
- Apply weighted scoring based on organizational priorities
- Consider interdependencies and portfolio effects
- Evaluate risk-adjusted returns and success probabilities

### 2. Portfolio Optimization
- Balance quick wins with long-term strategic investments
- Ensure diverse portfolio across different opportunity types
- Consider resource allocation and capacity constraints
- Optimize for maximum strategic value within constraints

### 3. Implementation Planning
- Develop phased implementation roadmaps
- Identify critical dependencies and prerequisites
- Plan resource allocation and team assignments
- Create milestone and success tracking frameworks

### 4. Risk Assessment
- Evaluate implementation risks and mitigation strategies
- Assess market and competitive risks
- Consider technical and organizational feasibility
- Plan contingency approaches for high-risk opportunities

## Output Format
```json
{
  "prioritization_framework": {
    "evaluation_criteria": [
      {
        "criterion_id": "user_impact",
        "criterion_name": "User Impact Potential",
        "description": "Potential to improve user experience and satisfaction",
        "weight": 0.25,
        "scoring_method": "quantitative",
        "measurement_approach": "affected_users × satisfaction_improvement"
      },
      {
        "criterion_id": "strategic_alignment",
        "criterion_name": "Strategic Alignment",
        "description": "Alignment with business objectives and strategic priorities",
        "weight": 0.20,
        "scoring_method": "qualitative",
        "measurement_approach": "expert_assessment_1_to_5_scale"
      }
    ],
    "scoring_methodology": {
      "scale": "0-100",
      "normalization": "weighted_average",
      "tie_breaking": "strategic_alignment_priority"
    }
  },
  "prioritized_opportunities": [
    {
      "opportunity_id": "OPP_001",
      "hmw_question": "How might we help Adaptive Explorers discover advanced features without overwhelming Cautious Learners?",
      "priority_rank": 1,
      "total_score": 87.5,
      "tier": "tier_1_critical",
      "evaluation_scores": {
        "user_impact": 92,
        "strategic_alignment": 88,
        "implementation_feasibility": 75,
        "business_value": 85,
        "innovation_potential": 90,
        "risk_level": 35
      },
      "rationale": "High user impact across multiple archetypes with strong strategic alignment and manageable implementation complexity",
      "target_outcomes": {
        "primary_metrics": [
          "Advanced feature adoption rate increase by 40%",
          "User confidence scores maintained above 4.2/5",
          "Support ticket reduction of 25%"
        ],
        "timeline": "6-9 months",
        "success_probability": 0.78
      },
      "implementation_requirements": {
        "resources": {
          "development_effort": "8-12 person-months",
          "design_effort": "3-4 person-months",
          "research_validation": "2-3 person-months"
        },
        "dependencies": [
          "User analytics platform upgrade",
          "A/B testing infrastructure",
          "Design system updates"
        ],
        "critical_success_factors": [
          "Accurate user profiling system",
          "Seamless progressive disclosure",
          "Effective user feedback loops"
        ]
      },
      "risk_assessment": {
        "technical_risks": {
          "complexity": "medium",
          "mitigation": "Phased rollout with pilot testing"
        },
        "market_risks": {
          "user_adoption": "low",
          "competitive_response": "medium"
        },
        "organizational_risks": {
          "resource_availability": "medium",
          "stakeholder_alignment": "low"
        }
      }
    }
  ],
  "portfolio_analysis": {
    "tier_distribution": {
      "tier_1_critical": 3,
      "tier_2_important": 7,
      "tier_3_valuable": 12,
      "tier_4_future": 8
    },
    "investment_allocation": {
      "quick_wins": 0.30,
      "strategic_initiatives": 0.50,
      "innovation_bets": 0.20
    },
    "timeline_distribution": {
      "0_3_months": 4,
      "3_6_months": 6,
      "6_12_months": 8,
      "12_plus_months": 5
    },
    "resource_requirements": {
      "total_development_effort": "45-60 person-months",
      "total_design_effort": "18-24 person-months",
      "total_research_effort": "12-15 person-months"
    }
  },
  "implementation_roadmap": {
    "phase_1_foundation": {
      "timeframe": "Months 1-3",
      "opportunities": ["OPP_003", "OPP_007"],
      "focus": "Quick wins and infrastructure preparation",
      "success_criteria": ["User analytics implementation", "Initial feature adoption improvements"],
      "resource_allocation": "30% of total capacity"
    },
    "phase_2_core_initiatives": {
      "timeframe": "Months 4-9",
      "opportunities": ["OPP_001", "OPP_002"],
      "focus": "Major strategic initiatives and adaptive systems",
      "success_criteria": ["Progressive disclosure system launch", "Archetype-based personalization"],
      "resource_allocation": "50% of total capacity"
    },
    "phase_3_innovation": {
      "timeframe": "Months 10-15",
      "opportunities": ["OPP_005", "OPP_009"],
      "focus": "Advanced AI-powered features and market differentiation",
      "success_criteria": ["Predictive interface system", "Advanced learning analytics"],
      "resource_allocation": "20% of total capacity"
    }
  },
  "decision_recommendations": {
    "immediate_actions": [
      "Approve Tier 1 opportunities for immediate planning",
      "Allocate resources for Phase 1 foundation work",
      "Establish success metrics and tracking systems"
    ],
    "strategic_decisions": [
      "Commit to adaptive interface strategy as core differentiator",
      "Invest in user analytics and personalization infrastructure",
      "Build cross-functional team for progressive disclosure development"
    ],
    "risk_mitigation": [
      "Implement phased rollout approach for major initiatives",
      "Establish user feedback loops for early validation",
      "Create contingency plans for high-risk opportunities"
    ]
  },
  "success_tracking": {
    "portfolio_metrics": [
      "Overall user satisfaction improvement",
      "Feature adoption rate increases",
      "Support cost reduction",
      "Competitive differentiation measures"
    ],
    "monitoring_framework": {
      "review_frequency": "monthly",
      "adjustment_triggers": ["significant_metric_changes", "resource_constraints", "market_shifts"],
      "escalation_criteria": ["tier_1_opportunity_failure", "resource_overrun_20_percent"]
    }
  }
}
```

## Quality Criteria
- **Objectivity**: Prioritization based on clear, measurable criteria
- **Strategic Alignment**: Opportunities support business objectives
- **Feasibility**: Implementation requirements are realistic and achievable
- **Balance**: Portfolio includes mix of quick wins and strategic investments
- **Actionability**: Prioritization leads to clear implementation decisions

## Error Handling
- **Conflicting priorities**: Use weighted scoring to resolve trade-offs
- **Resource constraints**: Adjust portfolio to fit available capacity
- **Uncertain estimates**: Include confidence intervals and sensitivity analysis
- **Stakeholder disagreement**: Document different perspectives and rationale
- **Changing conditions**: Build flexibility for priority adjustments

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.2 for systematic, analytical prioritization
- Configure max tokens for comprehensive analysis (12000-16000)
- Enable structured output for decision-making frameworks

### Input Configuration
- Accept HMW questions and business context data
- Configure evaluation criteria and weighting parameters
- Set up resource constraint and timeline inputs
- Include stakeholder preference and priority settings

### Output Configuration
- Structure for presentation generation and executive reporting
- Include decision-making frameworks and recommendation data
- Set up tracking and monitoring system configurations
- Configure stakeholder communication and approval workflows

## Example Prompts

### Primary Opportunity Prioritization Prompt
```
You are a strategic portfolio manager prioritizing innovation opportunities based on multiple criteria.

HMW OPPORTUNITIES: {hmw_question_collection}
BUSINESS CONTEXT: {strategic_objectives_and_constraints}
EVALUATION CRITERIA: {prioritization_framework}

PRIORITIZATION OBJECTIVES:
1. Evaluate opportunities against strategic, user, and business criteria
2. Create balanced portfolio of quick wins and strategic investments
3. Develop implementation roadmap with resource allocation
4. Assess risks and create mitigation strategies
5. Provide clear recommendations for decision-making

PRIORITIZATION PROCESS:
1. Score each opportunity against evaluation criteria
2. Apply weighted scoring based on strategic priorities
3. Assess implementation feasibility and resource requirements
4. Evaluate portfolio balance and optimization
5. Create phased implementation roadmap

For each opportunity, provide:
- Comprehensive scoring against all criteria
- Implementation requirements and dependencies
- Risk assessment and mitigation strategies
- Timeline and resource allocation recommendations
- Success metrics and tracking approaches

Focus on creating actionable prioritization that guides strategic resource allocation.
```

### Portfolio Optimization Prompt
```
Optimize this opportunity portfolio for maximum strategic value within constraints:

SCORED OPPORTUNITIES: {prioritized_opportunity_list}
RESOURCE CONSTRAINTS: {available_capacity_and_budget}
STRATEGIC OBJECTIVES: {business_goals_and_priorities}

OPTIMIZATION OBJECTIVES:
1. Maximize strategic value within resource constraints
2. Balance quick wins with long-term strategic investments
3. Ensure portfolio diversity across opportunity types
4. Minimize implementation risks and dependencies
5. Create realistic timeline and resource allocation

OPTIMIZATION PROCESS:
1. Analyze opportunity interdependencies and synergies
2. Balance portfolio across different investment categories
3. Optimize resource allocation for maximum impact
4. Create phased implementation approach
5. Develop contingency plans for high-risk opportunities

Provide optimized portfolio with:
- Balanced investment allocation across categories
- Phased implementation roadmap with timelines
- Resource allocation and capacity planning
- Risk mitigation and contingency strategies
- Success tracking and adjustment mechanisms

Focus on creating implementable portfolio that maximizes strategic value.
```

### Decision Framework Prompt
```
Create decision-making framework and recommendations for this prioritized portfolio:

PRIORITIZED PORTFOLIO: {optimized_opportunity_portfolio}
ORGANIZATIONAL CONTEXT: {decision_making_structure}
IMPLEMENTATION CONSTRAINTS: {resource_and_timeline_limits}

DECISION FRAMEWORK OBJECTIVES:
1. Provide clear recommendations for immediate actions
2. Create decision criteria for ongoing portfolio management
3. Establish success metrics and tracking systems
4. Define escalation and adjustment procedures
5. Ensure stakeholder alignment and buy-in

Framework components:
- Immediate action recommendations with rationale
- Strategic decision requirements and implications
- Success metrics and monitoring approaches
- Risk mitigation and contingency planning
- Stakeholder communication and approval processes

Focus on creating actionable decision framework that enables effective portfolio execution.
```
