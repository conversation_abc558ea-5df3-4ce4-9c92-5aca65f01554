# Step 5: Segmentation

## Step Overview
Segmentation breaks down preprocessed text into meaningful, analyzable units while maintaining context and relationships. This step creates logical boundaries around concepts, themes, and ideas to enable systematic analysis in subsequent steps.

## AI Agent Role
The AI agent acts as a **Content Segmentation Analyst** responsible for:
- Identifying natural breakpoints in text based on topic shifts
- Creating coherent segments that maintain semantic integrity
- Preserving context and relationships between segments
- Ensuring segments are appropriately sized for analysis
- Maintaining traceability to original source material

## Input Requirements
- **Preprocessed text documents** from Step 4 with:
  - Clean, standardized text content
  - Document metadata and source information
  - Quality scores and processing notes
- **Segmentation parameters** including:
  - Target segment length (words/sentences)
  - Overlap requirements between segments
  - Minimum/maximum segment sizes
  - Context preservation rules

## Processing Guidelines

### 1. Semantic Boundary Detection
- Identify topic shifts and thematic changes
- Recognize natural paragraph and section breaks
- Detect speaker changes in interviews/conversations
- Identify question-answer pairs in surveys/interviews

### 2. Segment Size Optimization
- Target 50-200 words per segment for optimal analysis
- Ensure segments contain complete thoughts/concepts
- Allow overlap when concepts span boundaries
- Maintain minimum viable segment size (25+ words)

### 3. Context Preservation
- Include relevant context from surrounding segments
- Maintain speaker attribution in conversational data
- Preserve temporal sequence and logical flow
- Link related segments through metadata

### 4. Quality Assurance
- Verify segment coherence and completeness
- Check for orphaned or incomplete thoughts
- Ensure balanced segment distribution
- Validate context preservation

## Output Format
```json
{
  "segmented_documents": [
    {
      "document_id": "doc_001",
      "source_metadata": {
        "participant_id": "P001",
        "source_type": "interview",
        "original_length": 1450
      },
      "segments": [
        {
          "segment_id": "doc_001_seg_001",
          "sequence_number": 1,
          "text": "Complete segment text with full context...",
          "word_count": 85,
          "context": {
            "speaker": "Participant",
            "topic_hint": "Initial experience description",
            "preceding_context": "Brief context from previous segment",
            "following_context": "Brief context from next segment"
          },
          "boundaries": {
            "start_char": 0,
            "end_char": 425,
            "boundary_type": "topic_shift",
            "confidence": 0.92
          },
          "metadata": {
            "segment_type": "narrative",
            "contains_quotes": true,
            "emotional_indicators": ["frustration", "confusion"],
            "key_concepts": ["onboarding", "first impression"]
          }
        }
      ],
      "segmentation_summary": {
        "total_segments": 12,
        "average_segment_length": 95,
        "segmentation_method": "semantic_boundary",
        "quality_score": 0.89
      }
    }
  ],
  "processing_summary": {
    "total_documents_processed": 25,
    "total_segments_created": 287,
    "average_segments_per_document": 11.5,
    "segmentation_quality": {
      "coherence_score": 0.91,
      "completeness_score": 0.94,
      "context_preservation_score": 0.88
    }
  }
}
```

## Quality Criteria
- **Coherence**: Each segment contains complete, related thoughts
- **Completeness**: No important information lost at boundaries
- **Context**: Sufficient context preserved for standalone analysis
- **Balance**: Segments are reasonably similar in size and scope
- **Traceability**: Clear mapping back to original source material

## Error Handling
- **Unclear boundaries**: Use conservative approach, prefer larger segments
- **Very short documents**: Create single segment with full context
- **Complex nested content**: Maintain hierarchical relationships
- **Missing context**: Flag segments requiring additional context
- **Segmentation failures**: Fall back to paragraph-based segmentation

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.2 for consistent but flexible segmentation
- Configure max tokens for processing multiple documents (6000-10000)
- Enable function calling for structured output

### Input Configuration
- Accept JSON from text preprocessing step
- Configure batch processing for document collections
- Set up parameter passing for segmentation rules
- Include quality threshold settings

### Output Configuration
- Structure as JSON array for coding steps
- Include segment metadata for analysis tracking
- Set up quality monitoring and alerts
- Configure segment size distribution reporting

## Example Prompts

### Primary Segmentation Prompt
```
You are a content segmentation specialist. Break down this preprocessed text into meaningful, analyzable segments.

SEGMENTATION RULES:
- Target 50-200 words per segment
- Maintain semantic coherence within segments
- Preserve context and relationships
- Identify natural topic boundaries
- Include speaker attribution where relevant

DOCUMENT TO SEGMENT:
Document ID: {document_id}
Source Type: {source_type}
Text: {preprocessed_text}

Create segments with:
1. Clear boundaries based on topic/speaker changes
2. Sufficient context for standalone analysis
3. Metadata about content type and key concepts
4. Traceability to original positions

Provide structured output with segment details and quality assessment.
```

### Boundary Detection Prompt
```
Analyze this text to identify optimal segmentation boundaries:

TEXT: {text_chunk}

Identify:
1. Natural topic transitions
2. Speaker changes (if applicable)
3. Concept completion points
4. Logical paragraph breaks

For each potential boundary, provide:
- Position in text
- Boundary type (topic_shift, speaker_change, concept_end)
- Confidence score (0-1)
- Reasoning for boundary placement

Focus on creating coherent, analyzable segments.
```

### Quality Validation Prompt
```
Evaluate the quality of these text segments:

ORIGINAL TEXT: {original_text}
SEGMENTS: {segment_list}

Assess:
1. Semantic coherence within each segment
2. Completeness of thoughts and concepts
3. Context preservation across boundaries
4. Appropriate segment sizing
5. Logical flow and relationships

Provide overall quality score (0-1) and specific improvement suggestions.
```
