# Step 5: Interview Segmentation

## Step Overview
Interview segmentation breaks down preprocessed interview transcripts into meaningful, analyzable response units while preserving conversational context and participant voice. This step creates logical boundaries around individual responses, topic discussions, and thematic exchanges to enable systematic analysis of interview content.

## AI Agent Role
The AI agent acts as an **Interview Content Analyst** responsible for:
- Identifying natural response boundaries within interview conversations
- Creating coherent segments that preserve question-answer relationships
- Maintaining participant voice and conversational context
- Ensuring segments capture complete thoughts and responses
- Preserving traceability to specific interview moments and participants

## Input Requirements
- **Preprocessed interview transcripts** from Step 4 with:
  - Clean, standardized interview content with speaker identification
  - Question-answer pair structures
  - Interview metadata and participant information
  - Quality assessments and transcription confidence scores
- **Interview segmentation parameters** including:
  - Response-based segmentation preferences
  - Topic transition detection sensitivity
  - Context preservation requirements for follow-up questions
  - Minimum response completeness thresholds

## Processing Guidelines

### 1. Interview Response Boundary Detection
- Identify complete participant responses to specific questions
- Recognize topic transitions within extended responses
- Detect follow-up question and clarification exchanges
- Preserve interviewer prompts that provide essential context

### 2. Response-Based Segment Optimization
- Create segments around complete participant responses (typically 50-300 words)
- Ensure each segment captures a complete thought or story
- Include interviewer questions that frame participant responses
- Maintain response integrity even for longer narratives

### 3. Conversational Context Preservation
- Maintain clear participant attribution for each response segment
- Preserve interviewer questions that provide response context
- Link related responses across different interview topics
- Maintain temporal flow of interview conversation

### 4. Interview-Specific Quality Assurance
- Verify each segment contains complete participant responses
- Check that interviewer context is appropriately included
- Ensure no response fragments are orphaned from their questions
- Validate that participant voice and meaning are preserved

## Output Format
```json
{
  "segmented_interviews": [
    {
      "interview_id": "INT_001",
      "participant_metadata": {
        "participant_id": "P001",
        "interview_date": "2024-01-15",
        "interview_duration": 45,
        "interviewer": "researcher_001"
      },
      "response_segments": [
        {
          "segment_id": "INT_001_R001",
          "sequence_number": 1,
          "interviewer_question": "Can you tell me about your first experience with the platform?",
          "participant_response": "Well, honestly, it was pretty confusing at first [laughs]. I couldn't figure out where anything was, and the menu system didn't make sense to me. I spent probably 20 minutes just trying to find the basic features I needed.",
          "word_count": 45,
          "response_context": {
            "question_type": "experience_exploration",
            "response_completeness": "complete",
            "emotional_tone": "frustrated_but_reflective",
            "follow_up_needed": false
          },
          "interview_position": {
            "timestamp_start": "00:02:15",
            "timestamp_end": "00:03:45",
            "topic_area": "initial_experience",
            "interview_phase": "opening"
          },
          "analysis_metadata": {
            "response_type": "narrative_experience",
            "contains_specific_examples": true,
            "emotional_indicators": ["laughter", "frustration"],
            "key_themes": ["confusion", "navigation_difficulty", "time_investment"],
            "usability_insights": ["menu_system_issues", "feature_discoverability"]
          }
        }
      ],
      "segmentation_summary": {
        "total_response_segments": 18,
        "average_response_length": 67,
        "complete_responses": 16,
        "partial_responses": 2,
        "segmentation_method": "question_response_pairs",
        "context_preservation_score": 0.94
      }
    }
  ],
  "processing_summary": {
    "total_interviews_processed": 12,
    "total_response_segments_created": 156,
    "average_segments_per_interview": 13,
    "segmentation_quality": {
      "response_completeness_score": 0.89,
      "context_preservation_score": 0.92,
      "participant_voice_retention": 0.95
    }
  }
}
```

## Quality Criteria
- **Response Completeness**: Each segment contains complete participant responses
- **Question-Answer Integrity**: Clear relationship between questions and responses maintained
- **Participant Voice**: Authentic participant language and meaning preserved
- **Conversational Context**: Sufficient interviewer context included for understanding
- **Interview Traceability**: Clear mapping back to specific interview moments and participants

## Error Handling
- **Incomplete responses**: Include partial responses with completion flags
- **Unclear question boundaries**: Use conservative approach, include full context
- **Complex multi-part responses**: Maintain response unity while noting internal structure
- **Missing interviewer context**: Flag segments requiring additional question context
- **Segmentation failures**: Fall back to complete question-answer pair preservation

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.2 for consistent but flexible segmentation
- Configure max tokens for processing multiple documents (6000-10000)
- Enable function calling for structured output

### Input Configuration
- Accept JSON from text preprocessing step
- Configure batch processing for document collections
- Set up parameter passing for segmentation rules
- Include quality threshold settings

### Output Configuration
- Structure as JSON array for coding steps
- Include segment metadata for analysis tracking
- Set up quality monitoring and alerts
- Configure segment size distribution reporting

## Example Prompts

### Primary Interview Segmentation Prompt
```
You are an interview content segmentation specialist. Break down this preprocessed interview transcript into meaningful response-based segments for systematic analysis.

INTERVIEW SEGMENTATION RULES:
- Create segments around complete participant responses (50-300 words typically)
- Maintain question-answer pair relationships
- Preserve participant voice and conversational context
- Include interviewer questions that frame responses
- Identify natural topic transitions within responses

INTERVIEW TO SEGMENT:
Interview ID: {interview_id}
Participant ID: {participant_id}
Preprocessed Transcript: {preprocessed_interview_transcript}

Create response segments with:
1. Complete participant responses to specific questions
2. Relevant interviewer questions for context
3. Metadata about response type and themes
4. Traceability to specific interview moments
5. Analysis-ready structure for coding steps

Provide structured output with response segments and interview-specific quality assessment.
```

### Boundary Detection Prompt
```
Analyze this text to identify optimal segmentation boundaries:

TEXT: {text_chunk}

Identify:
1. Natural topic transitions
2. Speaker changes (if applicable)
3. Concept completion points
4. Logical paragraph breaks

For each potential boundary, provide:
- Position in text
- Boundary type (topic_shift, speaker_change, concept_end)
- Confidence score (0-1)
- Reasoning for boundary placement

Focus on creating coherent, analyzable segments.
```

### Quality Validation Prompt
```
Evaluate the quality of these text segments:

ORIGINAL TEXT: {original_text}
SEGMENTS: {segment_list}

Assess:
1. Semantic coherence within each segment
2. Completeness of thoughts and concepts
3. Context preservation across boundaries
4. Appropriate segment sizing
5. Logical flow and relationships

Provide overall quality score (0-1) and specific improvement suggestions.
```
