# Step 9: Affinity Mapping

## Step Overview
Affinity mapping creates visual and conceptual relationships between categories, revealing deeper connections, dependencies, and system-level patterns. This step transforms hierarchical category structures into network-based relationship maps that illuminate the complex interdependencies within the research domain.

## AI Agent Role
The AI agent acts as a **Relationship Mapping Specialist** responsible for:
- Identifying conceptual affinities between categories
- Mapping causal and correlational relationships
- Creating network structures from categorical data
- Discovering emergent system-level patterns
- Visualizing complex interdependencies for analysis

## Input Requirements
- **Category system** from Step 8 with:
  - Hierarchical category structures
  - Code assignments and frequencies
  - Cross-cutting dimensions and themes
  - Category definitions and properties
- **Original coded segments** for:
  - Relationship evidence validation
  - Context-specific connection analysis
  - Co-occurrence pattern detection
- **Mapping parameters** including:
  - Relationship strength thresholds
  - Network density preferences
  - Visualization complexity levels
  - Pattern detection sensitivity

## Processing Guidelines

### 1. Relationship Identification
- Analyze co-occurrence patterns between categories
- Identify causal and temporal relationships
- Detect functional dependencies and enablers
- Map contradictory and complementary relationships

### 2. Affinity Strength Assessment
- Calculate relationship strength based on evidence
- Weight relationships by frequency and confidence
- Consider contextual factors affecting connections
- Validate relationships across different data sources

### 3. Network Structure Creation
- Build network graphs with categories as nodes
- Create weighted edges representing relationship strength
- Identify clusters and communities within the network
- Detect central nodes and peripheral elements

### 4. Pattern Recognition
- Identify recurring relationship patterns
- Detect system-level emergent properties
- Map feedback loops and cyclical relationships
- Recognize structural similarities across domains

## Output Format
```json
{
  "affinity_map": {
    "nodes": [
      {
        "node_id": "USABILITY_CHALLENGES",
        "node_type": "primary_category",
        "label": "Usability Challenges",
        "properties": {
          "size": 45,
          "centrality_score": 0.78,
          "influence_level": "high",
          "emotional_valence": "negative",
          "temporal_stage": "early_to_ongoing"
        },
        "position": {
          "x": 150,
          "y": 200,
          "cluster": "user_barriers"
        }
      }
    ],
    "edges": [
      {
        "edge_id": "USABILITY_TO_ADAPTATION",
        "source": "USABILITY_CHALLENGES",
        "target": "USER_ADAPTATION",
        "relationship_type": "causal_trigger",
        "strength": 0.84,
        "direction": "unidirectional",
        "evidence": {
          "co_occurrence_frequency": 32,
          "supporting_segments": 28,
          "confidence": 0.87,
          "temporal_pattern": "challenges_precede_adaptation"
        },
        "properties": {
          "delay_factor": "immediate_to_delayed",
          "consistency": "high",
          "context_dependency": "medium"
        },
        "description": "Usability challenges consistently trigger adaptive user behaviors"
      }
    ],
    "clusters": [
      {
        "cluster_id": "user_barriers",
        "name": "User Barrier System",
        "description": "Interconnected challenges that impede user progress",
        "member_nodes": ["USABILITY_CHALLENGES", "TECHNICAL_LIMITATIONS", "KNOWLEDGE_GAPS"],
        "cluster_properties": {
          "cohesion_score": 0.82,
          "internal_density": 0.67,
          "external_connections": 15
        },
        "emergent_patterns": ["barrier_amplification", "cascading_failures"]
      }
    ]
  },
  "relationship_patterns": [
    {
      "pattern_id": "CHALLENGE_ADAPTATION_CYCLE",
      "pattern_name": "Challenge-Adaptation Feedback Cycle",
      "pattern_type": "feedback_loop",
      "description": "Users encounter challenges, develop adaptations, which reveal new challenges",
      "components": [
        "Initial Challenge",
        "Adaptive Response",
        "Partial Success",
        "New Challenge Discovery",
        "Refined Adaptation"
      ],
      "frequency": 18,
      "strength": 0.79,
      "implications": "Suggests iterative design approach needed"
    }
  ],
  "system_insights": [
    {
      "insight_id": "CENTRAL_ADAPTATION_HUB",
      "insight_text": "User adaptation strategies serve as central hub connecting all other experience categories",
      "evidence": {
        "centrality_measures": {
          "betweenness": 0.89,
          "closeness": 0.76,
          "degree": 12
        },
        "supporting_relationships": 15
      },
      "implications": "Adaptation capability is key leverage point for experience improvement"
    }
  ],
  "network_metrics": {
    "total_nodes": 24,
    "total_edges": 67,
    "network_density": 0.23,
    "average_clustering": 0.45,
    "modularity": 0.67,
    "small_world_coefficient": 2.3,
    "most_central_nodes": ["USER_ADAPTATION", "EMOTIONAL_RESPONSE", "TASK_COMPLETION"]
  }
}
```

## Quality Criteria
- **Validity**: Relationships supported by clear evidence from data
- **Completeness**: All significant relationships identified and mapped
- **Coherence**: Network structure reflects logical system organization
- **Insight Generation**: Map reveals non-obvious patterns and connections
- **Actionability**: Relationships suggest clear intervention points

## Error Handling
- **Weak relationships**: Filter out connections below confidence threshold
- **Circular dependencies**: Identify and document feedback loops appropriately
- **Isolated nodes**: Investigate for missing connections or categorization errors
- **Overly complex networks**: Simplify by focusing on strongest relationships
- **Contradictory relationships**: Document conflicts and contextual factors

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.3 for balanced systematic and creative analysis
- Configure max tokens for complex network analysis (15000-20000)
- Enable visualization data generation for network graphs

### Input Configuration
- Accept category system and coded segment data
- Configure relationship detection parameters
- Set up network complexity controls
- Include visualization preferences

### Output Configuration
- Structure for quantitative analysis and pattern detection
- Include network visualization data (JSON/GraphML)
- Set up relationship validation workflows
- Configure insight extraction and reporting

## Example Prompts

### Primary Affinity Mapping Prompt
```
You are a systems analyst creating relationship maps between research categories.

CATEGORY SYSTEM: {category_system}
CODED SEGMENTS: {coded_segments}

MAPPING OBJECTIVES:
1. Identify relationships between categories based on data evidence
2. Assess relationship strength and directionality
3. Create network structure revealing system patterns
4. Discover emergent properties and feedback loops
5. Generate actionable insights from relationship patterns

ANALYSIS TASKS:
1. Examine co-occurrence patterns between categories
2. Identify causal, temporal, and functional relationships
3. Assess relationship strength using evidence frequency and confidence
4. Map network structure with nodes and weighted edges
5. Detect clusters, central nodes, and system-level patterns

Provide comprehensive affinity map with:
- Node and edge specifications
- Relationship evidence and validation
- Network clusters and communities
- Emergent patterns and feedback loops
- System-level insights and implications
```

### Relationship Validation Prompt
```
Validate the strength and validity of these category relationships:

PROPOSED RELATIONSHIPS: {relationships}
SUPPORTING EVIDENCE: {evidence_data}

VALIDATION CRITERIA:
1. Evidence frequency and consistency
2. Relationship directionality and causality
3. Contextual factors affecting connections
4. Statistical significance of co-occurrence
5. Logical coherence with domain knowledge

For each relationship, assess:
- Evidence quality and quantity
- Relationship strength (0-1 scale)
- Directionality and causality
- Contextual dependencies
- Confidence in relationship validity

Provide validated relationship set with confidence scores and recommendations.
```

### Pattern Discovery Prompt
```
Identify emergent patterns and system-level insights from this affinity map:

NETWORK STRUCTURE: {network_data}
RELATIONSHIP PATTERNS: {relationship_patterns}

PATTERN ANALYSIS:
1. Feedback loops and cyclical relationships
2. Central hubs and peripheral elements
3. Cluster formations and communities
4. Cascade effects and amplification patterns
5. System bottlenecks and leverage points

For each pattern identified, provide:
- Pattern description and components
- Frequency and strength measures
- System-level implications
- Potential intervention points
- Theoretical or practical significance

Focus on patterns that reveal non-obvious system dynamics and actionable insights.
```
