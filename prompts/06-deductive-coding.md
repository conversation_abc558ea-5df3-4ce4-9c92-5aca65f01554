# Step 6: Deductive Coding

## Step Overview
Deductive coding applies predetermined theoretical frameworks, research questions, or hypothesis-driven codes to text segments. This step systematically categorizes content based on existing knowledge structures, enabling theory-driven analysis and validation of research assumptions.

## AI Agent Role
The AI agent acts as a **Theoretical Framework Analyst** responsible for:
- Applying predefined coding schemes to text segments
- Identifying evidence for theoretical constructs
- Maintaining consistency in code application
- Documenting coding rationale and confidence levels
- Flagging segments that don't fit existing frameworks

## Input Requirements
- **Segmented text data** from Step 5 with:
  - Individual text segments with metadata
  - Context and source information
  - Segment quality scores
- **Coding framework** including:
  - Predefined code definitions and criteria
  - Theoretical constructs to identify
  - Research questions to address
  - Coding rules and decision trees
- **Configuration parameters** such as:
  - Confidence thresholds for code assignment
  - Multiple coding allowances
  - Framework priority hierarchies

## Processing Guidelines

### 1. Framework Application
- Apply codes systematically across all segments
- Use exact definitions provided in coding framework
- Maintain consistency in interpretation
- Document reasoning for each code assignment

### 2. Evidence Identification
- Look for explicit mentions of theoretical constructs
- Identify implicit evidence through context analysis
- Distinguish between direct and inferential evidence
- Note strength and quality of evidence

### 3. Code Assignment Rules
- Assign primary codes based on strongest evidence
- Include secondary codes for additional themes
- Use confidence scores to indicate certainty
- Flag ambiguous or borderline cases

### 4. Quality Control
- Cross-reference codes with framework definitions
- Ensure logical consistency across segments
- Validate code assignments against examples
- Document any framework limitations encountered

## Output Format
```json
{
  "deductive_coding_results": [
    {
      "segment_id": "doc_001_seg_001",
      "original_text": "Complete segment text...",
      "source_metadata": {
        "document_id": "doc_001",
        "participant_id": "P001",
        "source_type": "interview"
      },
      "applied_codes": [
        {
          "code_id": "USABILITY_BARRIER",
          "code_name": "Usability Barrier",
          "framework": "Technology Acceptance Model",
          "confidence": 0.89,
          "evidence_type": "explicit",
          "evidence_text": "I couldn't figure out how to navigate the menu system",
          "coding_rationale": "Direct mention of interface navigation difficulty",
          "subcodes": ["NAVIGATION_DIFFICULTY", "INTERFACE_CONFUSION"]
        },
        {
          "code_id": "EMOTIONAL_RESPONSE",
          "code_name": "Negative Emotional Response",
          "framework": "User Experience Framework",
          "confidence": 0.76,
          "evidence_type": "implicit",
          "evidence_text": "It was really frustrating",
          "coding_rationale": "Explicit emotional language indicating negative affect"
        }
      ],
      "coding_summary": {
        "total_codes_applied": 2,
        "primary_framework": "Technology Acceptance Model",
        "coding_confidence": 0.83,
        "requires_review": false
      }
    }
  ],
  "framework_coverage": {
    "Technology Acceptance Model": {
      "codes_applied": 45,
      "segments_coded": 38,
      "coverage_percentage": 67.8,
      "most_frequent_codes": ["USABILITY_BARRIER", "PERCEIVED_USEFULNESS"]
    },
    "User Experience Framework": {
      "codes_applied": 32,
      "segments_coded": 28,
      "coverage_percentage": 49.1,
      "most_frequent_codes": ["EMOTIONAL_RESPONSE", "SATISFACTION"]
    }
  },
  "coding_summary": {
    "total_segments_processed": 287,
    "segments_with_codes": 245,
    "average_codes_per_segment": 1.8,
    "uncoded_segments": 42,
    "overall_confidence": 0.81,
    "framework_gaps_identified": 15
  }
}
```

## Quality Criteria
- **Consistency**: Same codes applied to similar content across segments
- **Accuracy**: Codes match framework definitions and criteria
- **Completeness**: All relevant codes identified in each segment
- **Confidence**: High certainty in code assignments (>0.7)
- **Documentation**: Clear rationale provided for each coding decision

## Error Handling
- **Ambiguous content**: Assign multiple codes with confidence scores
- **Framework gaps**: Flag segments that don't fit existing codes
- **Low confidence**: Mark for human review and validation
- **Conflicting codes**: Document conflicts and provide reasoning
- **Missing frameworks**: Suggest additional theoretical perspectives

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.1 for consistent, systematic coding
- Configure max tokens for processing multiple segments (8000-12000)
- Enable function calling for structured code assignment

### Input Configuration
- Accept segmented data and coding framework definitions
- Configure framework loading from external files/databases
- Set up batch processing for segment collections
- Include confidence threshold parameters

### Output Configuration
- Structure as JSON for pattern analysis steps
- Include coding statistics and coverage metrics
- Set up quality monitoring and review queues
- Configure framework performance tracking

## Example Prompts

### Primary Deductive Coding Prompt
```
You are a systematic coding analyst applying theoretical frameworks to research data.

CODING FRAMEWORK:
{framework_definitions}

CODING RULES:
1. Apply codes only when clear evidence exists
2. Use exact framework definitions provided
3. Assign confidence scores (0-1) for each code
4. Provide specific evidence text and rationale
5. Flag segments that don't fit any codes

SEGMENT TO CODE:
Segment ID: {segment_id}
Text: {segment_text}
Context: {segment_context}

Apply relevant codes from the framework, providing:
- Code assignments with confidence scores
- Evidence text supporting each code
- Coding rationale and decision process
- Any framework limitations or gaps identified

Be systematic and consistent in your application.
```

### Code Validation Prompt
```
Validate this deductive coding assignment:

FRAMEWORK: {framework_name}
CODE DEFINITION: {code_definition}
ASSIGNED CODE: {assigned_code}
EVIDENCE: {evidence_text}
CONFIDENCE: {confidence_score}

Evaluate:
1. Does the evidence clearly support the code assignment?
2. Is the confidence score appropriate?
3. Are there alternative codes that might fit better?
4. Is the coding rationale sound?

Provide validation score (0-1) and improvement suggestions.
```

### Framework Gap Analysis Prompt
```
Analyze segments that couldn't be coded with existing frameworks:

UNCODED SEGMENTS: {uncoded_segments}
AVAILABLE FRAMEWORKS: {framework_list}

Identify:
1. Common themes in uncoded content
2. Potential framework extensions needed
3. Alternative theoretical perspectives to consider
4. Patterns suggesting new code categories

Suggest framework improvements or additional theories to incorporate.
```
