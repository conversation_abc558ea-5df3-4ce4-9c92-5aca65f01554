# Step 6: Interview Deductive Coding

## Step Overview
Interview deductive coding applies predetermined theoretical frameworks and research questions to interview response segments. This step systematically categorizes participant responses based on existing knowledge structures, enabling theory-driven analysis of interview insights and validation of research hypotheses.

## AI Agent Role
The AI agent acts as an **Interview Analysis Framework Specialist** responsible for:
- Applying predefined coding schemes to participant response segments
- Identifying evidence for theoretical constructs in participant narratives
- Maintaining consistency in code application across different interviews
- Documenting coding rationale with specific participant quotes
- Flagging interview responses that don't fit existing frameworks

## Input Requirements
- **Segmented interview data** from Step 5 with:
  - Individual participant response segments
  - Question-answer context and interview metadata
  - Response completeness and quality scores
- **Interview coding framework** including:
  - Predefined codes relevant to interview research questions
  - Theoretical constructs specific to the research domain
  - Interview guide alignment and question mapping
  - Participant response interpretation guidelines
- **Interview-specific parameters** such as:
  - Confidence thresholds for response interpretation
  - Multiple coding allowances for complex responses
  - Framework priority for overlapping theoretical perspectives

## Processing Guidelines

### 1. Interview Framework Application
- Apply codes systematically across all participant response segments
- Use exact definitions provided in interview coding framework
- Maintain consistency in interpretation across different participants
- Document reasoning for each code assignment with specific participant quotes

### 2. Participant Response Evidence Identification
- Look for explicit mentions of theoretical constructs in participant language
- Identify implicit evidence through participant stories and examples
- Distinguish between direct participant statements and inferential interpretations
- Note strength and quality of evidence within interview context

### 3. Interview-Specific Code Assignment Rules
- Assign primary codes based on strongest evidence in participant responses
- Include secondary codes for additional themes mentioned by participants
- Use confidence scores reflecting interpretation certainty of participant meaning
- Flag ambiguous responses or unclear participant statements

### 4. Interview Quality Control
- Cross-reference codes with framework definitions and participant intent
- Ensure logical consistency across different interview responses
- Validate code assignments against participant examples and stories
- Document framework limitations when participant responses don't fit existing codes

## Output Format
```json
{
  "deductive_coding_results": [
    {
      "segment_id": "doc_001_seg_001",
      "original_text": "Complete segment text...",
      "source_metadata": {
        "document_id": "doc_001",
        "participant_id": "P001",
        "source_type": "interview"
      },
      "applied_codes": [
        {
          "code_id": "USABILITY_BARRIER",
          "code_name": "Usability Barrier",
          "framework": "Technology Acceptance Model",
          "confidence": 0.89,
          "evidence_type": "explicit",
          "evidence_text": "I couldn't figure out how to navigate the menu system",
          "coding_rationale": "Direct mention of interface navigation difficulty",
          "subcodes": ["NAVIGATION_DIFFICULTY", "INTERFACE_CONFUSION"]
        },
        {
          "code_id": "EMOTIONAL_RESPONSE",
          "code_name": "Negative Emotional Response",
          "framework": "User Experience Framework",
          "confidence": 0.76,
          "evidence_type": "implicit",
          "evidence_text": "It was really frustrating",
          "coding_rationale": "Explicit emotional language indicating negative affect"
        }
      ],
      "coding_summary": {
        "total_codes_applied": 2,
        "primary_framework": "Technology Acceptance Model",
        "coding_confidence": 0.83,
        "requires_review": false
      }
    }
  ],
  "framework_coverage": {
    "Technology Acceptance Model": {
      "codes_applied": 45,
      "segments_coded": 38,
      "coverage_percentage": 67.8,
      "most_frequent_codes": ["USABILITY_BARRIER", "PERCEIVED_USEFULNESS"]
    },
    "User Experience Framework": {
      "codes_applied": 32,
      "segments_coded": 28,
      "coverage_percentage": 49.1,
      "most_frequent_codes": ["EMOTIONAL_RESPONSE", "SATISFACTION"]
    }
  },
  "coding_summary": {
    "total_segments_processed": 287,
    "segments_with_codes": 245,
    "average_codes_per_segment": 1.8,
    "uncoded_segments": 42,
    "overall_confidence": 0.81,
    "framework_gaps_identified": 15
  }
}
```

## Quality Criteria
- **Consistency**: Same codes applied to similar content across segments
- **Accuracy**: Codes match framework definitions and criteria
- **Completeness**: All relevant codes identified in each segment
- **Confidence**: High certainty in code assignments (>0.7)
- **Documentation**: Clear rationale provided for each coding decision

## Error Handling
- **Ambiguous content**: Assign multiple codes with confidence scores
- **Framework gaps**: Flag segments that don't fit existing codes
- **Low confidence**: Mark for human review and validation
- **Conflicting codes**: Document conflicts and provide reasoning
- **Missing frameworks**: Suggest additional theoretical perspectives

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.1 for consistent, systematic coding
- Configure max tokens for processing multiple segments (8000-12000)
- Enable function calling for structured code assignment

### Input Configuration
- Accept segmented data and coding framework definitions
- Configure framework loading from external files/databases
- Set up batch processing for segment collections
- Include confidence threshold parameters

### Output Configuration
- Structure as JSON for pattern analysis steps
- Include coding statistics and coverage metrics
- Set up quality monitoring and review queues
- Configure framework performance tracking

## Example Prompts

### Primary Interview Deductive Coding Prompt
```
You are a systematic interview coding analyst applying theoretical frameworks to participant response data.

INTERVIEW CODING FRAMEWORK:
{framework_definitions}

INTERVIEW CODING RULES:
1. Apply codes only when clear evidence exists in participant responses
2. Use exact framework definitions while respecting participant language
3. Assign confidence scores (0-1) based on clarity of participant intent
4. Provide specific participant quotes as evidence
5. Flag participant responses that don't fit existing framework codes

PARTICIPANT RESPONSE TO CODE:
Interview ID: {interview_id}
Participant ID: {participant_id}
Interviewer Question: {interviewer_question}
Participant Response: {participant_response}
Response Context: {response_context}

Apply relevant codes from the framework, providing:
- Code assignments with confidence scores
- Direct participant quotes supporting each code
- Coding rationale based on participant intent and meaning
- Any framework limitations when participant responses don't fit existing codes

Be systematic and consistent while preserving participant voice and meaning.
```

### Code Validation Prompt
```
Validate this deductive coding assignment:

FRAMEWORK: {framework_name}
CODE DEFINITION: {code_definition}
ASSIGNED CODE: {assigned_code}
EVIDENCE: {evidence_text}
CONFIDENCE: {confidence_score}

Evaluate:
1. Does the evidence clearly support the code assignment?
2. Is the confidence score appropriate?
3. Are there alternative codes that might fit better?
4. Is the coding rationale sound?

Provide validation score (0-1) and improvement suggestions.
```

### Framework Gap Analysis Prompt
```
Analyze segments that couldn't be coded with existing frameworks:

UNCODED SEGMENTS: {uncoded_segments}
AVAILABLE FRAMEWORKS: {framework_list}

Identify:
1. Common themes in uncoded content
2. Potential framework extensions needed
3. Alternative theoretical perspectives to consider
4. Patterns suggesting new code categories

Suggest framework improvements or additional theories to incorporate.
```
