# Step 14: Interview-Based HMW Generation

## Step Overview
Interview-based How Might We (HMW) generation transforms participant insights and interview-derived archetypes into actionable design challenges and innovation opportunities. This step creates specific, solution-oriented questions grounded in participant experiences that guide ideation, design thinking, and strategic planning processes.

## AI Agent Role
The AI agent acts as an **Interview-Informed Design Challenge Facilitator** responsible for:
- Converting participant insights into actionable HMW questions
- Creating solution-oriented design challenges based on participant needs and pain points
- Generating innovation opportunities from interview findings and participant narratives
- Prioritizing HMW questions by participant impact and implementation feasibility
- Ensuring HMW questions inspire creative solutions that address real participant needs

## Input Requirements
- **User archetypes** from Step 13 with:
  - Detailed persona profiles and characteristics
  - User journey stages and touchpoints
  - Pain points and opportunity areas
  - Design implications and recommendations
- **Strategic insights** including:
  - Key findings and patterns
  - Opportunity areas and challenges
  - Strategic recommendations
  - Business context and constraints
- **HMW generation parameters** such as:
  - Scope and focus areas
  - Innovation ambition levels
  - Resource and timeline constraints
  - Target audience for ideation

## Processing Guidelines

### 1. Opportunity Identification
- Extract specific pain points and challenges from archetypes
- Identify gaps between current and desired experiences
- Recognize unmet needs and latent opportunities
- Map systemic issues requiring innovative solutions

### 2. HMW Question Formulation
- Frame challenges as open-ended, solution-oriented questions
- Use "How might we..." format to inspire ideation
- Ensure questions are specific enough to be actionable
- Balance ambition with feasibility constraints

### 3. Question Categorization
- Group HMW questions by theme and scope
- Categorize by implementation timeline and complexity
- Organize by target archetype and user journey stage
- Prioritize by strategic impact and innovation potential

### 4. Validation and Refinement
- Test HMW questions for clarity and actionability
- Validate against research insights and archetype needs
- Refine questions based on stakeholder feedback
- Ensure questions inspire diverse solution approaches

## Output Format
```json
{
  "hmw_questions": [
    {
      "hmw_id": "HMW_001",
      "hmw_question": "How might we help Adaptive Explorers discover advanced features without overwhelming Cautious Learners?",
      "category": "progressive_disclosure",
      "scope": "interface_design",
      "target_archetypes": ["ADAPTIVE_EXPLORER", "CAUTIOUS_LEARNER"],
      "journey_stage": "exploration",
      "priority_level": "high",
      "innovation_ambition": "incremental",
      "source_insights": [
        "Users need adaptive challenges to develop mastery",
        "Progressive complexity enables successful learning"
      ],
      "supporting_evidence": {
        "pain_points": [
          "Explorers frustrated by oversimplified interfaces",
          "Learners overwhelmed by complex feature sets"
        ],
        "opportunity_size": "large",
        "affected_users": 0.67,
        "business_impact": "medium_to_high"
      },
      "success_criteria": [
        "Increased advanced feature adoption by Explorers",
        "Maintained confidence levels for Learners",
        "Reduced support tickets for feature discovery"
      ],
      "constraints": {
        "technical": ["existing_interface_framework", "mobile_compatibility"],
        "business": ["development_timeline", "resource_allocation"],
        "user": ["learning_curve_limitations", "cognitive_load"]
      },
      "ideation_prompts": [
        "What if interfaces could adapt in real-time to user expertise?",
        "How could we create 'training wheels' that gradually disappear?",
        "What would a personalized feature discovery system look like?"
      ]
    }
  ],
  "hmw_categories": [
    {
      "category_id": "progressive_disclosure",
      "category_name": "Progressive Disclosure & Adaptive Interfaces",
      "description": "Challenges related to revealing complexity gradually based on user readiness",
      "hmw_count": 8,
      "strategic_importance": "high",
      "implementation_complexity": "medium",
      "cross_archetype_impact": true
    }
  ],
  "opportunity_themes": [
    {
      "theme_id": "ADAPTIVE_LEARNING_SYSTEMS",
      "theme_name": "Adaptive Learning Systems",
      "description": "Opportunities to create systems that adapt to individual learning styles and progression",
      "related_hmw_questions": 12,
      "innovation_potential": "transformational",
      "market_differentiation": "high",
      "technical_feasibility": "medium"
    }
  ],
  "prioritization_matrix": {
    "high_impact_low_effort": [
      {
        "hmw_id": "HMW_003",
        "question": "How might we provide contextual hints that appear just when users need them?",
        "impact_score": 0.85,
        "effort_score": 0.3,
        "priority_rank": 1
      }
    ],
    "high_impact_high_effort": [
      {
        "hmw_id": "HMW_001",
        "question": "How might we help Adaptive Explorers discover advanced features without overwhelming Cautious Learners?",
        "impact_score": 0.92,
        "effort_score": 0.75,
        "priority_rank": 2
      }
    ],
    "low_impact_low_effort": [],
    "low_impact_high_effort": []
  },
  "innovation_roadmap": {
    "immediate_opportunities": [
      {
        "timeframe": "1-3 months",
        "hmw_questions": ["HMW_003", "HMW_007"],
        "focus": "Quick wins with contextual help and progressive onboarding"
      }
    ],
    "medium_term_opportunities": [
      {
        "timeframe": "6-12 months",
        "hmw_questions": ["HMW_001", "HMW_005"],
        "focus": "Adaptive interface systems and personalization"
      }
    ],
    "long_term_opportunities": [
      {
        "timeframe": "12+ months",
        "hmw_questions": ["HMW_002", "HMW_009"],
        "focus": "AI-powered learning systems and predictive interfaces"
      }
    ]
  },
  "ideation_guidelines": {
    "brainstorming_principles": [
      "Build on user research insights",
      "Consider multiple archetype needs simultaneously",
      "Think beyond current technical constraints",
      "Focus on user value over feature complexity"
    ],
    "solution_evaluation_criteria": [
      "Addresses core user needs identified in research",
      "Feasible within organizational constraints",
      "Scalable across different user types",
      "Measurable impact on user experience"
    ]
  }
}
```

## Quality Criteria
- **Actionability**: HMW questions inspire specific, implementable solutions
- **Clarity**: Questions are clear, focused, and unambiguous
- **Research-Grounded**: Questions directly address insights from user research
- **Innovation Potential**: Questions open up creative solution spaces
- **Strategic Alignment**: Questions support business objectives and user needs

## Error Handling
- **Vague questions**: Refine to be more specific and actionable
- **Solution-biased questions**: Reframe to be truly open-ended
- **Overwhelming scope**: Break down into smaller, manageable challenges
- **Misaligned priorities**: Validate against strategic objectives
- **Insufficient evidence**: Strengthen connection to research insights

## n8n Configuration Notes

### Node Setup
- Use **OpenAI/Anthropic Chat Model** node with GPT-4 or Claude
- Set temperature to 0.4 for creative yet focused question generation
- Configure max tokens for comprehensive HMW development (10000-14000)
- Enable iterative refinement for question optimization

### Input Configuration
- Accept archetype profiles and strategic insights
- Configure scope and focus area parameters
- Set up prioritization criteria and constraints
- Include innovation ambition and timeline settings

### Output Configuration
- Structure for opportunity prioritization and ideation workflows
- Include prioritization matrix and roadmap data
- Set up stakeholder validation and feedback loops
- Configure ideation session planning and facilitation

## Example Prompts

### Primary HMW Generation Prompt
```
You are a design thinking facilitator creating How Might We questions from user research insights.

USER ARCHETYPES: {archetype_profiles}
STRATEGIC INSIGHTS: {research_insights}
BUSINESS CONTEXT: {organizational_constraints}

HMW GENERATION OBJECTIVES:
1. Transform insights and pain points into actionable design challenges
2. Create solution-oriented questions that inspire innovation
3. Balance user needs with business constraints and opportunities
4. Prioritize questions by impact potential and implementation feasibility
5. Ensure questions are specific enough to guide ideation effectively

HMW DEVELOPMENT PROCESS:
1. Extract specific pain points and opportunities from archetypes
2. Identify gaps between current and desired user experiences
3. Frame challenges as open-ended "How might we..." questions
4. Categorize questions by theme, scope, and complexity
5. Prioritize based on impact, feasibility, and strategic alignment

For each HMW question, provide:
- Clear, actionable question statement
- Target archetypes and journey stages
- Supporting evidence and pain points
- Success criteria and constraints
- Innovation potential and business impact
- Ideation prompts and solution directions

Focus on questions that inspire creative solutions while remaining grounded in research insights.
```

### HMW Prioritization Prompt
```
Prioritize these HMW questions using impact/effort analysis and strategic alignment:

HMW QUESTIONS: {hmw_question_list}
STRATEGIC PRIORITIES: {business_objectives}
RESOURCE CONSTRAINTS: {implementation_constraints}

PRIORITIZATION CRITERIA:
1. User impact potential and affected population size
2. Business value and strategic alignment
3. Implementation effort and resource requirements
4. Innovation potential and market differentiation
5. Risk level and success probability

PRIORITIZATION PROCESS:
1. Score each HMW on impact and effort dimensions
2. Assess strategic alignment with business objectives
3. Consider implementation constraints and dependencies
4. Evaluate innovation potential and competitive advantage
5. Create prioritized roadmap with timeline recommendations

Provide prioritization matrix with:
- Impact/effort scoring for each question
- Strategic alignment assessment
- Implementation roadmap with timelines
- Resource allocation recommendations
- Risk assessment and mitigation strategies

Focus on creating actionable prioritization that guides resource allocation and planning.
```

### Ideation Planning Prompt
```
Create ideation session plans and guidelines for these prioritized HMW questions:

PRIORITIZED HMW QUESTIONS: {priority_hmw_list}
TARGET PARTICIPANTS: {ideation_team_composition}
SESSION CONSTRAINTS: {time_and_resource_limits}

IDEATION PLANNING OBJECTIVES:
1. Design effective brainstorming sessions for each HMW question
2. Create facilitation guidelines and ideation prompts
3. Develop solution evaluation criteria based on research insights
4. Plan follow-up activities for concept development
5. Ensure sessions generate actionable, research-grounded solutions

For each HMW question, provide:
- Session structure and timing recommendations
- Specific ideation prompts and exercises
- Participant preparation materials
- Solution evaluation criteria and methods
- Follow-up activities and next steps

Focus on creating structured ideation processes that maximize creative output while maintaining research grounding.
```
